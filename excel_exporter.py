import logging
import os
from datetime import datetime
from typing import Dict, Any, List, Optional
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils import get_column_letter
from decimal import Decimal


class ExcelExporter:
    """Exportiert ER-Daten nach Excel"""

    def __init__(self):
        # Farb-Schema aus readme.md
        self.colors = {
            'text': '4a4a4a',
            'primary_dark': '0b3f7a',
            'primary_medium': '1681fb',
            'primary_light': 'e7f2ff'
        }

        # Styles
        self.header_font = Font(name='Calib<PERSON>', size=12,
                                bold=True, color=self.colors['primary_dark'])
        self.title_font = Font(name='<PERSON><PERSON><PERSON>', size=16,
                               bold=True, color=self.colors['primary_dark'])
        self.normal_font = Font(name='Calibri', size=11,
                                color=self.colors['text'])
        self.total_font = Font(name='Cal<PERSON>ri', size=11,
                               bold=True, color=self.colors['text'])

        self.header_fill = PatternFill(
            start_color=self.colors['primary_light'], end_color=self.colors['primary_light'], fill_type='solid')
        self.total_fill = PatternFill(
            start_color='f0f0f0', end_color='f0f0f0', fill_type='solid')

        self.center_alignment = Alignment(
            horizontal='center', vertical='center')
        self.left_alignment = Alignment(horizontal='left', vertical='center')
        self.right_alignment = Alignment(horizontal='right', vertical='center')

        self.thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

    def export(self, er_data: Dict[str, Any], firma: str, von_datum: datetime, bis_datum: datetime, logo_path: Optional[str] = None, filename: Optional[str] = None, mandant: Optional[str] = None) -> str:
        """
        Exportiert ER-Daten nach Excel

        Args:
            er_data: ER-Datenstruktur
            firma: Firmenname
            von_datum: Startdatum
            bis_datum: Enddatum
            filename: Dateiname (optional)

        Returns:
            Pfad zur erstellten Datei
        """
        if filename is None:
            # Neues Dateinamen-Format: yymmddhhmmss mandantenname ER nach KST dd.mm.yy - dd.mm.yy.xlsx
            now = datetime.now()
            timestamp = now.strftime('%y%m%d%H%M%S')
            von_str = von_datum.strftime('%d.%m.%y')
            bis_str = bis_datum.strftime('%d.%m.%y')
            mandant_name = mandant if mandant else "mandant"
            filename = f"{timestamp} {mandant_name} ER nach KST {von_str} - {bis_str}.xlsx"

        logging.info(f"Erstelle Excel-Datei: {filename}")

        # Workbook erstellen
        wb = Workbook()
        ws = wb.active
        ws.title = "Erfolgsrechnung"

        # Kostenstellen-Spalten vorbereiten
        departments = list(er_data['departments'].keys())
        dept_names = [er_data['departments'][dept_id]
                      for dept_id in departments]

        # Header erstellen (mit Anzahl Kostenstellen für Logo-Positionierung)
        self._create_header(ws, firma, von_datum, bis_datum,
                            logo_path, len(dept_names))

        self._create_column_headers(ws, dept_names)

        # ER-Daten einfügen
        current_row = self._insert_er_data(ws, er_data, departments)

        # Formatierung anwenden
        self._apply_formatting(ws, len(departments), current_row)

        # Spaltenbreiten anpassen
        self._adjust_column_widths(ws, len(departments))

        # Datei speichern
        wb.save(filename)
        logging.info(f"Excel-Datei erstellt: {filename}")

        return filename

    def _create_header(self, ws, firma: str, von_datum: datetime, bis_datum: datetime, logo_path: str = None, num_departments: int = 0):
        """Erstellt den Header der Excel-Datei"""
        # Titel
        ws['A1'] = "Mehrstufige Erfolgsrechnung nach Kostenstellen"
        ws['A1'].font = self.title_font

        # Logo einfügen (falls vorhanden)
        if logo_path and os.path.exists(logo_path):
            try:
                from openpyxl.drawing.image import Image
                img = Image(logo_path)
                # Seitenverhältnis beibehalten und auf maximale Größe skalieren
                max_width = 120
                max_height = 60

                # Aktuelles Seitenverhältnis berechnen
                aspect_ratio = img.width / img.height

                # Neue Größe berechnen (Seitenverhältnis beibehalten)
                if img.width > max_width or img.height > max_height:
                    if aspect_ratio > (max_width / max_height):
                        # Breite ist limitierender Faktor
                        img.width = max_width
                        img.height = max_width / aspect_ratio
                    else:
                        # Höhe ist limitierender Faktor
                        img.height = max_height
                        img.width = max_height * aspect_ratio

                # Logo dynamisch platzieren - eine Spalte vor der Total-Spalte
                # um Überlappung zu vermeiden
                # Spalten: A (Konto) + Kostenstellen + Total
                # Logo-Spalte ist bei Index: num_departments (letzte Kostenstellen-Spalte)
                # A=1, dann Kostenstellen, Logo in letzter Kostenstellen-Spalte
                logo_col_index = 1 + num_departments
                logo_col_letter = chr(ord('A') + logo_col_index - 1)
                logo_cell = f"{logo_col_letter}1"

                ws.add_image(img, logo_cell)
                logging.info(
                    f"Logo eingefügt: {logo_path} in Zelle {logo_cell} (Größe: {img.width:.0f}x{img.height:.0f})")
            except Exception as e:
                logging.warning(
                    f"Logo konnte nicht eingefügt werden: {str(e)}")

        # Firma und Periode
        ws['A3'] = f"Firma: {firma}"
        ws['A3'].font = self.header_font

        periode_text = f"Periode: {von_datum.strftime('%d.%m.%Y')} - {bis_datum.strftime('%d.%m.%Y')}"
        ws['A4'] = periode_text
        ws['A4'].font = self.header_font

        # Erstellungsdatum
        ws['A5'] = f"Erstellt am: {datetime.now().strftime('%d.%m.%Y %H:%M')}"
        ws['A5'].font = self.normal_font

    def _create_column_headers(self, ws, dept_names: List[str]):
        """Erstellt die Spalten-Header"""
        start_row = 8

        # Erste Spalte: Kontobezeichnung
        ws.cell(row=start_row, column=1, value="Konto")
        ws.cell(row=start_row, column=1).font = self.header_font
        ws.cell(row=start_row, column=1).fill = self.header_fill
        ws.cell(row=start_row, column=1).alignment = self.center_alignment

        # Kostenstellen-Spalten
        for i, dept_name in enumerate(dept_names, start=2):
            ws.cell(row=start_row, column=i, value=dept_name)
            ws.cell(row=start_row, column=i).font = self.header_font
            ws.cell(row=start_row, column=i).fill = self.header_fill
            ws.cell(row=start_row, column=i).alignment = self.center_alignment

        # Total-Spalte
        total_col = len(dept_names) + 2
        ws.cell(row=start_row, column=total_col, value="Total")
        ws.cell(row=start_row, column=total_col).font = self.header_font
        ws.cell(row=start_row, column=total_col).fill = self.header_fill
        ws.cell(row=start_row, column=total_col).alignment = self.center_alignment

    def _insert_er_data(self, ws, er_data: Dict[str, Any], departments: List[str]) -> int:
        """
        Fügt die ER-Daten ein

        Returns:
            Letzte verwendete Zeile
        """
        from data_processor import DataProcessor
        processor = DataProcessor()

        current_row = 9
        total_col = len(departments) + 2

        # ER-Struktur durchgehen
        for item in processor.er_structure:
            if item['type'] == 'category':
                # Kategorie-Header
                current_row += 1
                ws.cell(row=current_row, column=1, value=item['name'])
                ws.cell(row=current_row, column=1).font = self.total_font
                current_row += 1

                # Konten der Kategorie (sortiert)
                sorted_accounts = processor.get_sorted_accounts_for_category(
                    er_data, item['key'])
                for account_number, account_data in sorted_accounts:
                    # Kontoname
                    account_display = f"{account_number} {account_data['name']}"
                    ws.cell(row=current_row, column=1,
                            value=account_display)
                    ws.cell(row=current_row,
                            column=1).font = self.normal_font
                    ws.cell(row=current_row,
                            column=1).alignment = self.left_alignment

                    # Beträge pro Kostenstelle
                    row_total = Decimal('0')
                    for i, dept_id in enumerate(departments, start=2):
                        amount = account_data['department_saldos'].get(
                            dept_id, Decimal('0'))
                        if amount != 0:
                            ws.cell(row=current_row, column=i,
                                    value=float(amount))
                            ws.cell(row=current_row,
                                    column=i).number_format = '#,##0.00'
                        ws.cell(row=current_row,
                                column=i).font = self.normal_font
                        ws.cell(row=current_row,
                                column=i).alignment = self.right_alignment
                        row_total += amount

                    # Total-Spalte
                    if row_total != 0:
                        ws.cell(row=current_row, column=total_col,
                                value=float(row_total))
                        ws.cell(row=current_row,
                                column=total_col).number_format = '#,##0.00'
                    ws.cell(row=current_row,
                            column=total_col).font = self.normal_font
                    ws.cell(
                        row=current_row, column=total_col).alignment = self.right_alignment

                    current_row += 1

            elif item['type'] == 'total':
                # Total-Zeile
                ws.cell(row=current_row, column=1, value=item['name'])
                ws.cell(row=current_row, column=1).font = self.total_font
                ws.cell(row=current_row, column=1).fill = self.total_fill

                # Total-Beträge
                total_data = er_data['totals'].get(item['key'], {})
                row_total = Decimal('0')
                for i, dept_id in enumerate(departments, start=2):
                    amount = total_data.get(dept_id, Decimal('0'))
                    if amount != 0:
                        ws.cell(row=current_row, column=i, value=float(amount))
                        ws.cell(row=current_row,
                                column=i).number_format = '#,##0.00'
                    ws.cell(row=current_row, column=i).font = self.total_font
                    ws.cell(row=current_row, column=i).fill = self.total_fill
                    ws.cell(row=current_row,
                            column=i).alignment = self.right_alignment
                    row_total += amount

                # Total-Spalte
                if row_total != 0:
                    ws.cell(row=current_row, column=total_col,
                            value=float(row_total))
                    ws.cell(row=current_row,
                            column=total_col).number_format = '#,##0.00'
                ws.cell(row=current_row, column=total_col).font = self.total_font
                ws.cell(row=current_row, column=total_col).fill = self.total_fill
                ws.cell(row=current_row,
                        column=total_col).alignment = self.right_alignment

                current_row += 2

            elif item['type'] == 'calculation':
                # Berechnungszeile
                ws.cell(row=current_row, column=1, value=item['name'])
                ws.cell(row=current_row, column=1).font = self.total_font
                ws.cell(row=current_row, column=1).fill = self.total_fill

                # Berechnungs-Beträge
                calc_data = er_data['calculations'].get(item['key'], {})
                row_total = Decimal('0')
                for i, dept_id in enumerate(departments, start=2):
                    amount = calc_data.get(dept_id, Decimal('0'))
                    if amount != 0:
                        ws.cell(row=current_row, column=i, value=float(amount))
                        ws.cell(row=current_row,
                                column=i).number_format = '#,##0.00'
                    ws.cell(row=current_row, column=i).font = self.total_font
                    ws.cell(row=current_row, column=i).fill = self.total_fill
                    ws.cell(row=current_row,
                            column=i).alignment = self.right_alignment
                    row_total += amount

                # Total-Spalte
                if row_total != 0:
                    ws.cell(row=current_row, column=total_col,
                            value=float(row_total))
                    ws.cell(row=current_row,
                            column=total_col).number_format = '#,##0.00'
                ws.cell(row=current_row, column=total_col).font = self.total_font
                ws.cell(row=current_row, column=total_col).fill = self.total_fill
                ws.cell(row=current_row,
                        column=total_col).alignment = self.right_alignment

                current_row += 2

        return current_row

    def _apply_formatting(self, ws, num_departments: int, last_row: int):
        """Wendet Formatierung auf die Tabelle an"""
        # Rahmen um die gesamte Tabelle
        for row in range(8, last_row + 1):
            for col in range(1, num_departments + 3):
                ws.cell(row=row, column=col).border = self.thin_border

    def _adjust_column_widths(self, ws, num_departments: int):
        """Passt die Spaltenbreiten an"""
        # Erste Spalte (Kontobezeichnung) breiter
        ws.column_dimensions['A'].width = 40

        # Kostenstellen-Spalten
        for i in range(2, num_departments + 2):
            col_letter = get_column_letter(i)
            ws.column_dimensions[col_letter].width = 15

        # Total-Spalte
        total_col_letter = get_column_letter(num_departments + 2)
        ws.column_dimensions[total_col_letter].width = 15
